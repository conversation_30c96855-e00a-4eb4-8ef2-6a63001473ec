******************************************************************************
            TI ARM Clang Linker PC v4.0.0                      
******************************************************************************
>> Linked Fri Aug  1 13:20:36 2025

OUTPUT FILE NAME:   <TI_CAR1.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 000068cd


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  00008cf0  00017310  R  X
  SRAM                  20200000   00008000  00000709  000078f7  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    000075c8   000075c8    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00007508   00007508    r-x .text
000075d0    000075d0    00001728   00001728    r--
  000075d0    000075d0    000016b0   000016b0    r-- .rodata
  00008c80    00008c80    00000078   00000078    r-- .cinit
20200000    20200000    00000509   00000000    rw-
  20200000    20200000    000003d8   00000000    rw- .bss
  202003d8    202003d8    00000131   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00007508     
                  000000c0    000009d0     libc.a : _printfi.c.obj (.text:__TI_printfi)
                  00000a90    00000278     inv_mpu_dmp_motion_driver.o (.text.dmp_enable_feature)
                  00000d08    00000238     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_thresh)
                  00000f40    0000022c     inv_mpu.o (.text.mpu_reset_fifo)
                  0000116c    00000220     libc.a : _printfi.c.obj (.text._pconv_a)
                  0000138c    000001e0     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  0000156c    000001dc     libc.a : _printfi.c.obj (.text._pconv_g)
                  00001748    000001a0     inv_mpu.o (.text.mpu_set_bypass)
                  000018e8    00000192     libclang_rt.builtins.a : adddf3.S.obj (.text.adddf3_subdf3)
                  00001a7a    00000002                            : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  00001a7c    00000188     No_Mcu_Ganv_Grayscale_Sensor.o (.text.No_MCU_Ganv_Sensor_Init)
                  00001c04    00000178     inv_mpu_dmp_motion_driver.o (.text.dmp_set_orientation)
                  00001d7c    00000144     MPU6050.o (.text.MPU6050_Init)
                  00001ec0    0000013c     libc.a : _printfi.c.obj (.text.fcvt)
                  00001ffc    00000134     MPU6050.o (.text.mspm0_i2c_read)
                  00002130    00000134     libc.a : qsort.c.obj (.text.qsort)
                  00002264    00000130     OLED.o (.text.OLED_ShowChar)
                  00002394    00000130     inv_mpu.o (.text.mpu_set_sensors)
                  000024c4    0000012c     driver_app.o (.text.Motor_SetSpeed)
                  000025f0    00000128     Task_App.o (.text.Task_Tracker)
                  00002718    00000128     inv_mpu.o (.text.mpu_init)
                  00002840    00000124     PID_IQMath.o (.text.PID_IQ_Prosc)
                  00002964    00000124     inv_mpu.o (.text.mpu_load_firmware)
                  00002a88    00000120     libc.a : _printfi.c.obj (.text._pconv_e)
                  00002ba8    00000110     OLED.o (.text.OLED_Init)
                  00002cb8    0000010c     libclang_rt.builtins.a : divdf3.S.obj (.text.__divdf3)
                  00002dc4    00000104     driverlib.a : dl_timer.o (.text.DL_Timer_initFourCCPWMMode)
                  00002ec8    00000100     inv_mpu.o (.text.mpu_lp_accel_mode)
                  00002fc8    000000fc     Task_App.o (.text.Task_Init)
                  000030c4    000000f0     Motor.o (.text.Motor_SetDirc)
                  000031b4    000000f0     Task_App.o (.text.Task_Motor_PID)
                  000032a4    000000ec     inv_mpu.o (.text.mpu_set_sample_rate)
                  00003390    000000e4     Interrupt.o (.text.GROUP1_IRQHandler)
                  00003474    000000e4     libclang_rt.builtins.a : muldf3.S.obj (.text.__muldf3)
                  00003558    000000e4     inv_mpu.o (.text.mpu_set_accel_fsr)
                  0000363c    000000e0     Task_App.o (.text.Task_OLED)
                  0000371c    000000dc     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_configSYSPLL)
                  000037f8    000000dc     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Analog_value)
                  000038d4    000000d8     libc.a : s_scalbn.c.obj (.text.scalbn)
                  000039ac    000000d8     libclang_rt.builtins.a : addsf3.S.obj (.text)
                  00003a84    000000d4     inv_mpu.o (.text.set_int_enable)
                  00003b58    000000d0     inv_mpu.o (.text.mpu_set_lpf)
                  00003c28    000000c4     inv_mpu.o (.text.mpu_set_gyro_fsr)
                  00003cec    000000c4     MPU6050.o (.text.mspm0_i2c_write)
                  00003db0    000000bc     inv_mpu.o (.text.mpu_configure_fifo)
                  00003e6c    000000b8     inv_mpu.o (.text.mpu_set_dmp_state)
                  00003f24    000000b4     Task.o (.text.Task_Add)
                  00003fd8    000000ac     Task_App.o (.text.Task_Serial)
                  00004084    000000ac     inv_mpu.o (.text.mpu_read_mem)
                  00004130    000000ac     inv_mpu.o (.text.mpu_write_mem)
                  000041dc    000000aa     No_Mcu_Ganv_Grayscale_Sensor.o (.text.normalizeAnalogValues)
                  00004286    00000002     libclang_rt.builtins.a : aeabi_div0.c.obj (.text.__aeabi_ldiv0)
                  00004288    000000a2                            : udivmoddi4.S.obj (.text)
                  0000432a    00000002     --HOLE-- [fill = 0]
                  0000432c    000000a0     Motor.o (.text.Motor_SetDuty)
                  000043cc    000000a0     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  0000446c    0000009c     inv_mpu.o (.text.mpu_set_int_latched)
                  00004508    00000098     OLED.o (.text.I2C_OLED_WR_Byte)
                  000045a0    00000098     inv_mpu_dmp_motion_driver.o (.text.dmp_set_fifo_rate)
                  00004638    00000096     MPU6050.o (.text.inv_row_2_scale)
                  000046ce    00000090     driver_app.o (.text.Motor_Create)
                  0000475e    00000002     --HOLE-- [fill = 0]
                  00004760    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_Motor_PWM_init)
                  000047ec    0000008c     libclang_rt.builtins.a : mulsf3.S.obj (.text.__mulsf3)
                  00004878    00000084     ti_msp_dl_config.o (.text.SYSCFG_DL_UART0_init)
                  000048fc    00000084     ti_msp_dl_config.o (.text.__NVIC_SetPriority)
                  00004980    00000082     libclang_rt.builtins.a : divsf3.S.obj (.text.__divsf3)
                  00004a02    00000002     --HOLE-- [fill = 0]
                  00004a04    00000080     Motor.o (.text.Motor_GetSpeed)
                  00004a84    00000080     driver_app.o (.text.Speed_To_PWM)
                  00004b04    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  00004b80    00000074     libclang_rt.builtins.a : comparedf2.c.obj (.text.__gedf2)
                  00004bf4    00000074     inv_mpu.o (.text.mpu_get_accel_fsr)
                  00004c68    00000072     No_Mcu_Ganv_Grayscale_Sensor.o (.text.No_MCU_Ganv_Sensor_Init_Frist)
                  00004cda    00000002     --HOLE-- [fill = 0]
                  00004cdc    00000070     Serial.o (.text.MyPrintf_DMA)
                  00004d4c    0000006e     OLED.o (.text.OLED_ShowString)
                  00004dba    0000006c     No_Mcu_Ganv_Grayscale_Sensor.o (.text.convertAnalogToDigital)
                  00004e26    0000006a     OLED.o (.text.I2C_OLED_Clear)
                  00004e90    00000068     libclang_rt.builtins.a : comparedf2.c.obj (.text.__ledf2)
                  00004ef8    00000066     libc.a : _printfi.c.obj (.text._mcpy)
                  00004f5e    00000066     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_axes)
                  00004fc4    00000064     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_setHFCLKSourceHFXTParams)
                  00005028    00000064     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_OLED_init)
                  0000508c    00000062     libclang_rt.builtins.a : aeabi_dcmp.S.obj (.text.__aeabi_dcmp)
                  000050ee    00000002     --HOLE-- [fill = 0]
                  000050f0    00000062                            : aeabi_fcmp.S.obj (.text.__aeabi_fcmp)
                  00005152    00000002     --HOLE-- [fill = 0]
                  00005154    00000060     OLED.o (.text.I2C_OLED_i2c_sda_unlock)
                  000051b4    00000060     Key_Led.o (.text.Key_Read)
                  00005214    00000060     inv_mpu_dmp_motion_driver.o (.text.dmp_enable_gyro_cal)
                  00005274    00000060     MPU6050.o (.text.mpu6050_i2c_sda_unlock)
                  000052d4    00000060     inv_mpu.o (.text.mpu_get_gyro_fsr)
                  00005334    0000005e     driverlib.a : dl_i2c.o (.text.DL_I2C_fillControllerTXFIFO)
                  00005392    00000002     --HOLE-- [fill = 0]
                  00005394    0000005c     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  000053f0    0000005c     libc.a : s_frexp.c.obj (.text.frexp)
                  0000544c    0000005c     MPU6050.o (.text.mspm0_i2c_enable)
                  000054a8    0000005c     OLED.o (.text.mspm0_i2c_enable)
                  00005504    00000058     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_MPU6050_init)
                  0000555c    00000058     Serial.o (.text.Serial_Init)
                  000055b4    00000058     libc.a : _ltoa.c.obj (.text.__TI_ltoa)
                  0000560c    00000058            : _printfi.c.obj (.text._pconv_f)
                  00005664    00000056     libclang_rt.builtins.a : aeabi_idivmod.S.obj (.text.__aeabi_idivmod)
                  000056ba    00000052     libc.a : _printfi.c.obj (.text._ecpy)
                  0000570c    00000050     MPU6050.o (.text.DL_I2C_startControllerTransfer)
                  0000575c    00000050     OLED.o (.text.DL_I2C_startControllerTransfer)
                  000057ac    00000050     ti_msp_dl_config.o (.text.SysTick_Config)
                  000057fc    0000004c     ti_msp_dl_config.o (.text.DL_ADC12_initSingleSample)
                  00005848    0000004c     driverlib.a : dl_dma.o (.text.DL_DMA_initChannel)
                  00005894    0000004c     ti_msp_dl_config.o (.text.DL_UART_setBaudRateDivisor)
                  000058e0    0000004c     OLED.o (.text.OLED_Printf)
                  0000592c    0000004c     ti_msp_dl_config.o (.text.SYSCFG_DL_ADC1_init)
                  00005978    0000004a     ti_msp_dl_config.o (.text.DL_ADC12_configConversionMem)
                  000059c2    00000002     --HOLE-- [fill = 0]
                  000059c4    0000004a     libclang_rt.builtins.a : fixdfsi.S.obj (.text.__fixdfsi)
                  00005a0e    00000002     --HOLE-- [fill = 0]
                  00005a10    00000048     driverlib.a : dl_uart.o (.text.DL_UART_init)
                  00005a58    00000048     ADC.o (.text.adc_getValue)
                  00005aa0    00000048     inv_mpu_dmp_motion_driver.o (.text.dmp_enable_6x_lp_quat)
                  00005ae8    00000048     inv_mpu_dmp_motion_driver.o (.text.dmp_enable_lp_quat)
                  00005b30    00000048     MPU6050.o (.text.mspm0_i2c_disable)
                  00005b78    00000044     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
                  00005bbc    00000044     Task_App.o (.text.Task_Key)
                  00005c00    00000044     inv_mpu_dmp_motion_driver.o (.text.dmp_set_shake_reject_thresh)
                  00005c44    00000044     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_count)
                  00005c88    00000044     OLED.o (.text.mspm0_i2c_disable)
                  00005ccc    00000042     No_Mcu_Ganv_Grayscale_Sensor.o (.text.No_Mcu_Ganv_Sensor_Task_Without_tick)
                  00005d0e    00000002     --HOLE-- [fill = 0]
                  00005d10    00000042     libclang_rt.builtins.a : fixunsdfsi.S.obj (.text.__fixunsdfsi)
                  00005d52    00000002     --HOLE-- [fill = 0]
                  00005d54    00000040     driverlib.a : dl_adc12.o (.text.DL_ADC12_setClockConfig)
                  00005d94    00000040     Interrupt.o (.text.Interrupt_Init)
                  00005dd4    00000040     driver_app.o (.text.Motor_Init)
                  00005e14    00000040     Task_App.o (.text.Task_GraySensor)
                  00005e54    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  00005e94    00000040                            : extendsfdf2.S.obj (.text.__extendsfdf2)
                  00005ed4    00000040     libc.a : atoi.c.obj (.text.atoi)
                  00005f14    00000040            : vsnprintf.c.obj (.text.vsnprintf)
                  00005f54    0000003e     Task.o (.text.Task_CMP)
                  00005f92    0000003e     MPU6050.o (.text.inv_orientation_matrix_to_scalar)
                  00005fd0    0000003c     MPU6050.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  0000600c    0000003c     OLED.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00006048    0000003c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00006084    0000003c     driverlib.a : dl_i2c.o (.text.DL_I2C_flushControllerTXFIFO)
                  000060c0    0000003c     ti_msp_dl_config.o (.text.DL_Timer_setCounterControl)
                  000060fc    0000003c     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Anolog_Value)
                  00006138    0000003c     OLED.o (.text.I2C_OLED_Set_Pos)
                  00006174    0000003c     libclang_rt.builtins.a : floatsisf.S.obj (.text.__floatsisf)
                  000061b0    0000003c                            : comparesf2.S.obj (.text.__gtsf2)
                  000061ec    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  00006228    0000003a     driver_app.o (.text.Motor_ValidateParams)
                  00006262    00000002     --HOLE-- [fill = 0]
                  00006264    0000003a     libclang_rt.builtins.a : comparesf2.S.obj (.text.__eqsf2)
                  0000629e    00000002     --HOLE-- [fill = 0]
                  000062a0    0000003a                            : muldsi3.S.obj (.text.__muldsi3)
                  000062da    00000038     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Normalize_For_User)
                  00006312    00000002     --HOLE-- [fill = 0]
                  00006314    00000038     Task_App.o (.text.Task_LED)
                  0000634c    00000038     libclang_rt.builtins.a : fixsfsi.S.obj (.text.__fixsfsi)
                  00006384    00000034     MPU6050.o (.text.DL_GPIO_initDigitalInputFeatures)
                  000063b8    00000034     OLED.o (.text.DL_GPIO_initDigitalInputFeatures)
                  000063ec    00000034     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00006420    00000034     inv_mpu.o (.text.mpu_get_sample_rate)
                  00006454    00000032     inv_mpu_dmp_motion_driver.o (.text.dmp_set_shake_reject_time)
                  00006486    00000032     inv_mpu_dmp_motion_driver.o (.text.dmp_set_shake_reject_timeout)
                  000064b8    00000030     ADC.o (.text.DL_ADC12_getMemResult)
                  000064e8    00000030     Serial.o (.text.DL_DMA_setTransferSize)
                  00006518    00000030     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_CH_RX_init)
                  00006548    00000030     iqmath.a : _IQNtoF.o (.text._IQ24toF)
                  00006578    00000030     libc.a : _printfi.c.obj (.text._fcpy)
                  000065a8    00000030            : vsnprintf.c.obj (.text._outs)
                  000065d8    00000030     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_time)
                  00006608    00000030     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_time_multi)
                  00006638    0000002c     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  00006664    0000002c     Interrupt.o (.text.__NVIC_EnableIRQ)
                  00006690    0000002c     libclang_rt.builtins.a : floatsidf.S.obj (.text.__floatsidf)
                  000066bc    0000002c     main.o (.text.main)
                  000066e8    0000002c     libc.a : vsprintf.c.obj (.text.vsprintf)
                  00006714    00000028     MPU6050.o (.text.DL_Common_updateReg)
                  0000673c    00000028     OLED.o (.text.DL_Common_updateReg)
                  00006764    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  0000678c    00000028     Serial.o (.text.DL_DMA_setDestAddr)
                  000067b4    00000028     Serial.o (.text.DL_DMA_setSrcAddr)
                  000067dc    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerRXFIFOThreshold)
                  00006804    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerTXFIFOThreshold)
                  0000682c    00000028     ti_msp_dl_config.o (.text.DL_UART_setRXFIFOThreshold)
                  00006854    00000028     ti_msp_dl_config.o (.text.DL_UART_setTXFIFOThreshold)
                  0000687c    00000028     SysTick.o (.text.SysTick_Increasment)
                  000068a4    00000028     libclang_rt.builtins.a : floatunsisf.S.obj (.text.__floatunsisf)
                  000068cc    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  000068f4    00000026     Serial.o (.text.DL_DMA_disableChannel)
                  0000691a    00000026     Serial.o (.text.DL_DMA_enableChannel)
                  00006940    00000026     ti_msp_dl_config.o (.text.DL_I2C_setAnalogGlitchFilterPulseWidth)
                  00006966    00000026     driverlib.a : dl_i2c.o (.text.DL_I2C_setClockConfig)
                  0000698c    00000024     ti_msp_dl_config.o (.text.DL_UART_setRXInterruptTimeout)
                  000069b0    00000024     libclang_rt.builtins.a : floatunsidf.S.obj (.text.__floatunsidf)
                  000069d4    00000024                            : muldi3.S.obj (.text.__muldi3)
                  000069f8    00000022     libc.a : memccpy.c.obj (.text.memccpy)
                  00006a1a    00000002     --HOLE-- [fill = 0]
                  00006a1c    00000020     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunction)
                  00006a3c    00000020     ti_msp_dl_config.o (.text.DL_SYSCTL_setFlashWaitState)
                  00006a5c    00000020     SysTick.o (.text.Delay)
                  00006a7c    00000020     libc.a : memcmp.c.obj (.text.memcmp)
                  00006a9c    0000001e     ti_msp_dl_config.o (.text.DL_UART_setOversampling)
                  00006aba    00000002     --HOLE-- [fill = 0]
                  00006abc    0000001e     libclang_rt.builtins.a : ashldi3.S.obj (.text.__ashldi3)
                  00006ada    00000002     --HOLE-- [fill = 0]
                  00006adc    0000001c     ADC.o (.text.DL_ADC12_startConversion)
                  00006af8    0000001c     ADC.o (.text.DL_ADC12_stopConversion)
                  00006b14    0000001c     ti_msp_dl_config.o (.text.DL_DMA_enableInterrupt)
                  00006b30    0000001c     Interrupt.o (.text.DL_GPIO_clearInterruptStatus)
                  00006b4c    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_clearInterruptStatus)
                  00006b68    0000001c     MPU6050.o (.text.DL_GPIO_enableHiZ)
                  00006b84    0000001c     OLED.o (.text.DL_GPIO_enableHiZ)
                  00006ba0    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableHiZ)
                  00006bbc    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableInterrupt)
                  00006bd8    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralOutputFunction)
                  00006bf4    0000001c     ti_msp_dl_config.o (.text.DL_I2C_enableInterrupt)
                  00006c10    0000001c     MPU6050.o (.text.DL_I2C_getSDAStatus)
                  00006c2c    0000001c     OLED.o (.text.DL_I2C_getSDAStatus)
                  00006c48    0000001c     MPU6050.o (.text.DL_I2C_isControllerRXFIFOEmpty)
                  00006c64    0000001c     Interrupt.o (.text.DL_Interrupt_getPendingGroup)
                  00006c80    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  00006c9c    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setULPCLKDivider)
                  00006cb8    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  00006cd4    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  00006cf0    0000001c     ti_msp_dl_config.o (.text.DL_UART_enableInterrupt)
                  00006d0c    0000001c     inv_mpu_dmp_motion_driver.o (.text.dmp_load_motion_driver_firmware)
                  00006d28    00000018     ti_msp_dl_config.o (.text.DL_ADC12_enablePower)
                  00006d40    00000018     ti_msp_dl_config.o (.text.DL_ADC12_reset)
                  00006d58    00000018     ti_msp_dl_config.o (.text.DL_DMA_clearInterruptStatus)
                  00006d70    00000018     MPU6050.o (.text.DL_GPIO_enableOutput)
                  00006d88    00000018     OLED.o (.text.DL_GPIO_enableOutput)
                  00006da0    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  00006db8    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  00006dd0    00000018     Interrupt.o (.text.DL_GPIO_getEnabledInterruptStatus)
                  00006de8    00000018     MPU6050.o (.text.DL_GPIO_initDigitalOutput)
                  00006e00    00000018     OLED.o (.text.DL_GPIO_initDigitalOutput)
                  00006e18    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutput)
                  00006e30    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralAnalogFunction)
                  00006e48    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  00006e60    00000018     MPU6050.o (.text.DL_GPIO_setPins)
                  00006e78    00000018     Motor.o (.text.DL_GPIO_setPins)
                  00006e90    00000018     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_GPIO_setPins)
                  00006ea8    00000018     OLED.o (.text.DL_GPIO_setPins)
                  00006ec0    00000018     Task_App.o (.text.DL_GPIO_setPins)
                  00006ed8    00000018     driver_app.o (.text.DL_GPIO_setPins)
                  00006ef0    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setPins)
                  00006f08    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setUpperPinsPolarity)
                  00006f20    00000018     MPU6050.o (.text.DL_I2C_clearInterruptStatus)
                  00006f38    00000018     OLED.o (.text.DL_I2C_clearInterruptStatus)
                  00006f50    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableAnalogGlitchFilter)
                  00006f68    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableController)
                  00006f80    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableControllerClockStretching)
                  00006f98    00000018     MPU6050.o (.text.DL_I2C_enablePower)
                  00006fb0    00000018     OLED.o (.text.DL_I2C_enablePower)
                  00006fc8    00000018     ti_msp_dl_config.o (.text.DL_I2C_enablePower)
                  00006fe0    00000018     MPU6050.o (.text.DL_I2C_getRawInterruptStatus)
                  00006ff8    00000018     OLED.o (.text.DL_I2C_getRawInterruptStatus)
                  00007010    00000018     MPU6050.o (.text.DL_I2C_reset)
                  00007028    00000018     OLED.o (.text.DL_I2C_reset)
                  00007040    00000018     ti_msp_dl_config.o (.text.DL_I2C_reset)
                  00007058    00000018     ti_msp_dl_config.o (.text.DL_I2C_setTimerPeriod)
                  00007070    00000018     ti_msp_dl_config.o (.text.DL_MathACL_enablePower)
                  00007088    00000018     ti_msp_dl_config.o (.text.DL_MathACL_reset)
                  000070a0    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  000070b8    00000018     ti_msp_dl_config.o (.text.DL_Timer_enablePower)
                  000070d0    00000018     ti_msp_dl_config.o (.text.DL_Timer_reset)
                  000070e8    00000018     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  00007100    00000018     Interrupt.o (.text.DL_UART_clearInterruptStatus)
                  00007118    00000018     ti_msp_dl_config.o (.text.DL_UART_enableDMAReceiveEvent)
                  00007130    00000018     ti_msp_dl_config.o (.text.DL_UART_enableDMATransmitEvent)
                  00007148    00000018     ti_msp_dl_config.o (.text.DL_UART_enableFIFOs)
                  00007160    00000018     ti_msp_dl_config.o (.text.DL_UART_enablePower)
                  00007178    00000018     ti_msp_dl_config.o (.text.DL_UART_reset)
                  00007190    00000018     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_CH_TX_init)
                  000071a8    00000018     iqmath.a : _IQNdiv.o (.text._IQ24div)
                  000071c0    00000018              : _IQNmpy.o (.text._IQ24mpy)
                  000071d8    00000018     libc.a : vsnprintf.c.obj (.text._outc)
                  000071f0    00000018            : vsprintf.c.obj (.text._outs)
                  00007208    00000016     ADC.o (.text.DL_ADC12_disableConversions)
                  0000721e    00000016     ADC.o (.text.DL_ADC12_enableConversions)
                  00007234    00000016     ti_msp_dl_config.o (.text.DL_ADC12_enableConversions)
                  0000724a    00000016     Interrupt.o (.text.DL_GPIO_readPins)
                  00007260    00000016     Key_Led.o (.text.DL_GPIO_readPins)
                  00007276    00000016     MPU6050.o (.text.DL_GPIO_readPins)
                  0000728c    00000016     OLED.o (.text.DL_GPIO_readPins)
                  000072a2    00000016     MPU6050.o (.text.DL_I2C_transmitControllerData)
                  000072b8    00000016     ti_msp_dl_config.o (.text.DL_UART_enable)
                  000072ce    00000016     SysTick.o (.text.SysGetTick)
                  000072e4    00000016     libc.a : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init_nomemset)
                  000072fa    00000014     MPU6050.o (.text.DL_GPIO_clearPins)
                  0000730e    00000014     Motor.o (.text.DL_GPIO_clearPins)
                  00007322    00000014     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_GPIO_clearPins)
                  00007336    00000014     OLED.o (.text.DL_GPIO_clearPins)
                  0000734a    00000014     Task_App.o (.text.DL_GPIO_clearPins)
                  0000735e    00000014     driver_app.o (.text.DL_GPIO_clearPins)
                  00007372    00000014     ti_msp_dl_config.o (.text.DL_GPIO_clearPins)
                  00007386    00000002     --HOLE-- [fill = 0]
                  00007388    00000014     MPU6050.o (.text.DL_I2C_getControllerStatus)
                  0000739c    00000014     OLED.o (.text.DL_I2C_getControllerStatus)
                  000073b0    00000014     MPU6050.o (.text.DL_I2C_receiveControllerData)
                  000073c4    00000014     ti_msp_dl_config.o (.text.DL_I2C_resetControllerTransfer)
                  000073d8    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_disableSYSPLL)
                  000073ec    00000014     ti_msp_dl_config.o (.text.DL_Timer_enableClock)
                  00007400    00000014     ti_msp_dl_config.o (.text.DL_Timer_setCCPDirection)
                  00007414    00000014     libclang_rt.builtins.a : aeabi_uldivmod.S.obj (.text.__aeabi_uldivmod)
                  00007428    00000014     inv_mpu_dmp_motion_driver.o (.text.dmp_register_android_orient_cb)
                  0000743c    00000014     inv_mpu_dmp_motion_driver.o (.text.dmp_register_tap_cb)
                  00007450    00000014     libc.a : _printfi.c.obj (.text.strchr)
                  00007464    00000012     driverlib.a : dl_uart.o (.text.DL_UART_setClockConfig)
                  00007476    00000012     libc.a : memcpy16.S.obj (.text:TI_memcpy_small)
                  00007488    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  0000749a    00000010     ADC.o (.text.DL_ADC12_getStatus)
                  000074aa    00000002     --HOLE-- [fill = 0]
                  000074ac    00000010     ti_msp_dl_config.o (.text.DL_SYSCTL_disableHFXT)
                  000074bc    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  000074cc    00000010     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSTICK_init)
                  000074dc    00000010     libc.a : wcslen.c.obj (.text.wcslen)
                  000074ec    0000000e     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Digtal_For_User)
                  000074fa    00000002     --HOLE-- [fill = 0]
                  000074fc    0000000e     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memset)
                  0000750a    0000000e     libc.a : _printfi.c.obj (.text.strlen)
                  00007518    0000000e     MPU6050.o (.text.tap_cb)
                  00007526    0000000e     libc.a : memset16.S.obj (.text:TI_memset_small)
                  00007534    0000000c     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_init)
                  00007540    0000000c     SysTick.o (.text.Sys_GetTick)
                  0000754c    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  00007556    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
                  00007560    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dsub.1)
                  00007570    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_1)
                  0000757a    0000000a            : vsprintf.c.obj (.text._outc)
                  00007584    0000000a     MPU6050.o (.text.android_orient_cb)
                  0000758e    00000008     Interrupt.o (.text.SysTick_Handler)
                  00007596    00000002     --HOLE-- [fill = 0]
                  00007598    00000008     libc.a : aeabi_portable.c.obj (.text.__aeabi_errno_addr)
                  000075a0    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  000075a8    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  000075ac    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  000075b0    00000010     libc.a : boot_cortex_m.c.obj (.tramp._c_int00_noargs.1)
                  000075c0    00000004            : pre_init.c.obj (.text._system_pre_init)
                  000075c4    00000004            : exit.c.obj (.text:abort)

.cinit     0    00008c80    00000078     
                  00008c80    0000004e     (.cinit..data.load) [load image, compression = lzss]
                  00008cce    00000002     --HOLE-- [fill = 0]
                  00008cd0    0000000c     (__TI_handler_table)
                  00008cdc    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00008ce4    00000010     (__TI_cinit_table)
                  00008cf4    00000004     --HOLE-- [fill = 0]

.rodata    0    000075d0    000016b0     
                  000075d0    00000bf6     inv_mpu_dmp_motion_driver.o (.rodata.dmp_memory)
                  000081c6    000005f0     OLED_Font.o (.rodata.asc2_1608)
                  000087b6    00000228     OLED_Font.o (.rodata.asc2_0806)
                  000089de    00000002     ti_msp_dl_config.o (.rodata.gI2C_MPU6050ClockConfig)
                  000089e0    00000101     libc.a : aeabi_ctype.S.obj (.rodata:__aeabi_ctype_table_)
                  00008ae1    00000003     ti_msp_dl_config.o (.rodata.gMotor_PWMClockConfig)
                  00008ae4    00000028     ti_msp_dl_config.o (.rodata.gSYSPLLConfig)
                  00008b0c    00000028     inv_mpu.o (.rodata.test)
                  00008b34    0000001f     Task_App.o (.rodata.str1.59338935762404233141)
                  00008b53    0000001e     inv_mpu.o (.rodata.reg)
                  00008b71    00000002     ti_msp_dl_config.o (.rodata.gI2C_OLEDClockConfig)
                  00008b73    00000001     --HOLE-- [fill = 0]
                  00008b74    00000018     ti_msp_dl_config.o (.rodata.gDMA_CH_RXConfig)
                  00008b8c    00000018     ti_msp_dl_config.o (.rodata.gDMA_CH_TXConfig)
                  00008ba4    00000014     Task_App.o (.rodata.str1.157702741485139367601)
                  00008bb8    00000014     Task_App.o (.rodata.str1.183384535776591351011)
                  00008bcc    00000014     Task_App.o (.rodata.str1.97872905622636903301)
                  00008be0    00000011     libc.a : _printfi.c.obj (.rodata.str1.103488685894817597201)
                  00008bf1    00000011            : _printfi.c.obj (.rodata.str1.153638888446227384661)
                  00008c02    00000011     Task_App.o (.rodata.str1.57768648227965084991)
                  00008c13    00000001     --HOLE-- [fill = 0]
                  00008c14    0000000c     inv_mpu.o (.rodata.hw)
                  00008c20    0000000c     Task_App.o (.rodata.str1.25142174965186748781)
                  00008c2c    0000000b     Task_App.o (.rodata.str1.182657883079055368591)
                  00008c37    00000001     --HOLE-- [fill = 0]
                  00008c38    0000000a     ti_msp_dl_config.o (.rodata.gUART0Config)
                  00008c42    00000002     ti_msp_dl_config.o (.rodata.gUART0ClockConfig)
                  00008c44    00000008     ti_msp_dl_config.o (.rodata.gADC1ClockConfig)
                  00008c4c    00000008     ti_msp_dl_config.o (.rodata.gMotor_PWMConfig)
                  00008c54    00000008     Task_App.o (.rodata.str1.67400646179352630301)
                  00008c5c    00000007     Task_App.o (.rodata.str1.136405643080007560121)
                  00008c63    00000006     Task_App.o (.rodata.str1.115332825834609149281)
                  00008c69    00000005     Task_App.o (.rodata.str1.87978995337490384161)
                  00008c6e    00000004     Task_App.o (.rodata.str1.134609064190095881641)
                  00008c72    00000004     Task_App.o (.rodata.str1.171900814140190138471)
                  00008c76    0000000a     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    000003d8     UNINITIALIZED
                  20200000    00000200     (.common:Serial_RxData)
                  20200200    000000f0     Task.o (.bss.Task_Schedule)
                  202002f0    000000b0     (.common:GraySensor)
                  202003a0    00000014     (.common:left_motor)
                  202003b4    00000014     (.common:right_motor)
                  202003c8    00000004     (.common:Data_Pitch)
                  202003cc    00000004     (.common:Data_Roll)
                  202003d0    00000004     (.common:Data_Yaw)
                  202003d4    00000004     (.common:ExISR_Flag)

.data      0    202003d8    00000131     UNINITIALIZED
                  202003d8    00000048     Motor.o (.data.Motor_Left)
                  20200420    00000048     Motor.o (.data.Motor_Right)
                  20200468    0000002c     inv_mpu.o (.data.st)
                  20200494    00000010     Task_App.o (.data.Gray_Anolog)
                  202004a4    00000010     Task_App.o (.data.Gray_Normal)
                  202004b4    00000010     inv_mpu_dmp_motion_driver.o (.data.dmp)
                  202004c4    0000000e     MPU6050.o (.data.hal)
                  202004d2    00000009     MPU6050.o (.data.gyro_orientation)
                  202004db    00000008     Task_App.o (.data.Data_Tracker_Input)
                  202004e3    00000001     Task_App.o (.data.Flag_LED)
                  202004e4    00000008     Task_App.o (.data.Motor)
                  202004ec    00000004     Task_App.o (.data.Data_MotorEncoder)
                  202004f0    00000004     Task_App.o (.data.Data_Motor_TarSpeed)
                  202004f4    00000004     Task_App.o (.data.Data_Tracker_Offset)
                  202004f8    00000004     libc.a : aeabi_portable.c.obj (.data.__aeabi_errno)
                  202004fc    00000004     SysTick.o (.data.delayTick)
                  20200500    00000004     SysTick.o (.data.uwTick)
                  20200504    00000001     Interrupt.o (.data.Flag_MPU6050_Ready)
                  20200505    00000001     Task_App.o (.data.Gray_Digtal)
                  20200506    00000001     Task_App.o (.data.Task_Key.Key_Old)
                  20200507    00000001     Task.o (.data.Task_Num)
                  20200508    00000001     Interrupt.o (.data.enable_group1_irq)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                           code    ro data   rw data
       ------                           ----    -------   -------
    .\
       ti_msp_dl_config.o               3426    123       0      
       startup_mspm0g350x_ticlang.o     8       192       0      
       main.o                           44      0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           3478    315       0      
                                                                 
    .\APP\Src\
       Task_App.o                       1416    165       239    
       Interrupt.o                      470     0         6      
    +--+--------------------------------+-------+---------+---------+
       Total:                           1886    165       245    
                                                                 
    .\BSP\Src\
       OLED_Font.o                      0       2072      0      
       MPU6050.o                        1912    0         35     
       OLED.o                           1854    0         0      
       No_Mcu_Ganv_Grayscale_Sensor.o   1244    0         0      
       Serial.o                         404     0         512    
       driver_app.o                     738     0         40     
       Motor.o                          572     0         144    
       Task.o                           242     0         241    
       PID_IQMath.o                     292     0         0      
       ADC.o                            236     0         0      
       Key_Led.o                        118     0         0      
       SysTick.o                        106     0         8      
    +--+--------------------------------+-------+---------+---------+
       Total:                           7718    2072      980    
                                                                 
    .\DMP\
       inv_mpu_dmp_motion_driver.o      2470    3062      16     
       inv_mpu.o                        4336    82        44     
    +--+--------------------------------+-------+---------+---------+
       Total:                           6806    3144      60     
                                                                 
    C:/ti/ccstheia151/mspm0_sdk_2_05_01_00/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_sysctl_mspm0g1x0x_g3x0x.o     388     0         0      
       dl_timer.o                       356     0         0      
       dl_i2c.o                         192     0         0      
       dl_uart.o                        90      0         0      
       dl_dma.o                         76      0         0      
       dl_adc12.o                       64      0         0      
       dl_common.o                      10      0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           1176    0         0      
                                                                 
    C:/ti/ccstheia151/mspm0_sdk_2_05_01_00/source/ti/iqmath/lib/ticlang/m0p/mathacl/mspm0g1x0x_g3x0x/iqmath.a
       _IQNtoF.o                        48      0         0      
       _IQNdiv.o                        24      0         0      
       _IQNmpy.o                        24      0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           96      0         0      
                                                                 
    C:\ti\ccstheia151\ccs\tools\compiler\ti-cgt-armllvm_4.0.0.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       _printfi.c.obj                   4510    34        0      
       qsort.c.obj                      308     0         0      
       aeabi_ctype.S.obj                0       257       0      
       s_scalbn.c.obj                   216     0         0      
       vsnprintf.c.obj                  136     0         0      
       copy_decompress_lzss.c.obj       124     0         0      
       s_frexp.c.obj                    92      0         0      
       _ltoa.c.obj                      88      0         0      
       vsprintf.c.obj                   78      0         0      
       atoi.c.obj                       64      0         0      
       autoinit.c.obj                   60      0         0      
       boot_cortex_m.c.obj              56      0         0      
       memccpy.c.obj                    34      0         0      
       memcmp.c.obj                     32      0         0      
       copy_zero_init.c.obj             22      0         0      
       copy_decompress_none.c.obj       18      0         0      
       memcpy16.S.obj                   18      0         0      
       wcslen.c.obj                     16      0         0      
       memset16.S.obj                   14      0         0      
       aeabi_portable.c.obj             8       0         4      
       exit.c.obj                       4       0         0      
       pre_init.c.obj                   4       0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           5902    291       4      
                                                                 
    C:\ti\ccstheia151\ccs\tools\compiler\ti-cgt-armllvm_4.0.0.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       adddf3.S.obj                     418     0         0      
       divdf3.S.obj                     268     0         0      
       muldf3.S.obj                     228     0         0      
       comparedf2.c.obj                 220     0         0      
       addsf3.S.obj                     216     0         0      
       udivmoddi4.S.obj                 162     0         0      
       mulsf3.S.obj                     140     0         0      
       divsf3.S.obj                     130     0         0      
       comparesf2.S.obj                 118     0         0      
       aeabi_dcmp.S.obj                 98      0         0      
       aeabi_fcmp.S.obj                 98      0         0      
       aeabi_idivmod.S.obj              86      0         0      
       fixdfsi.S.obj                    74      0         0      
       fixunsdfsi.S.obj                 66      0         0      
       aeabi_uidivmod.S.obj             64      0         0      
       extendsfdf2.S.obj                64      0         0      
       floatsisf.S.obj                  60      0         0      
       muldsi3.S.obj                    58      0         0      
       fixsfsi.S.obj                    56      0         0      
       floatsidf.S.obj                  44      0         0      
       floatunsisf.S.obj                40      0         0      
       floatunsidf.S.obj                36      0         0      
       muldi3.S.obj                     36      0         0      
       ashldi3.S.obj                    30      0         0      
       aeabi_uldivmod.S.obj             20      0         0      
       aeabi_memset.S.obj               14      0         0      
       aeabi_memcpy.S.obj               8       0         0      
       aeabi_div0.c.obj                 4       0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           2856    0         0      
                                                                 
       Stack:                           0       0         512    
       Linker Generated:                0       114       0      
    +--+--------------------------------+-------+---------+---------+
       Grand Total:                     29918   6101      1801   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 00008ce4 records: 2, size/record: 8, table size: 16
	.data: load addr=00008c80, load size=0000004e bytes, run addr=202003d8, run size=00000131 bytes, compression=lzss
	.bss: load addr=00008cdc, load size=00000008 bytes, run addr=20200000, run size=000003d8 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 00008cd0 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


FAR CALL TRAMPOLINES

callee name               trampoline name
   callee addr  tramp addr   call addr  call info
--------------  -----------  ---------  ----------------
__aeabi_dsub              $Tramp$TT$L$PI$$__aeabi_dsub
   000018e9     00007560     0000755e   libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
_c_int00_noargs           $Tramp$TT$L$PI$$_c_int00_noargs
   000068cd     000075b0     000075ac   startup_mspm0g350x_ticlang.o (.text.Reset_Handler)

[2 trampolines]
[2 trampoline calls]


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                                 
-------   ----                                 
000075a9  ADC0_IRQHandler                      
000075a9  ADC1_IRQHandler                      
000075a9  AES_IRQHandler                       
000075c4  C$$EXIT                              
000075a9  CANFD0_IRQHandler                    
000075a9  DAC0_IRQHandler                      
00005d55  DL_ADC12_setClockConfig              
0000754d  DL_Common_delayCycles                
00005849  DL_DMA_initChannel                   
00005335  DL_I2C_fillControllerTXFIFO          
00006085  DL_I2C_flushControllerTXFIFO         
00006967  DL_I2C_setClockConfig                
0000371d  DL_SYSCTL_configSYSPLL               
00004fc5  DL_SYSCTL_setHFCLKSourceHFXTParams   
00005b79  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00002dc5  DL_Timer_initFourCCPWMMode           
00006cb9  DL_Timer_setCaptCompUpdateMethod     
000070e9  DL_Timer_setCaptureCompareOutCtl     
000074bd  DL_Timer_setCaptureCompareValue      
00006cd5  DL_Timer_setClockConfig              
00005a11  DL_UART_init                         
00007465  DL_UART_setClockConfig               
000075a9  DMA_IRQHandler                       
202004ec  Data_MotorEncoder                    
202004f0  Data_Motor_TarSpeed                  
202003c8  Data_Pitch                           
202003cc  Data_Roll                            
202004db  Data_Tracker_Input                   
202004f4  Data_Tracker_Offset                  
202003d0  Data_Yaw                             
000075a9  Default_Handler                      
00006a5d  Delay                                
202003d4  ExISR_Flag                           
202004e3  Flag_LED                             
20200504  Flag_MPU6050_Ready                   
000075a9  GROUP0_IRQHandler                    
00003391  GROUP1_IRQHandler                    
000037f9  Get_Analog_value                     
000060fd  Get_Anolog_Value                     
000074ed  Get_Digtal_For_User                  
000062db  Get_Normalize_For_User               
202002f0  GraySensor                           
20200494  Gray_Anolog                          
20200505  Gray_Digtal                          
202004a4  Gray_Normal                          
000075a9  HardFault_Handler                    
000075a9  I2C0_IRQHandler                      
000075a9  I2C1_IRQHandler                      
00004e27  I2C_OLED_Clear                       
00006139  I2C_OLED_Set_Pos                     
00004509  I2C_OLED_WR_Byte                     
00005155  I2C_OLED_i2c_sda_unlock              
00005d95  Interrupt_Init                       
000051b5  Key_Read                             
00001d7d  MPU6050_Init                         
202004e4  Motor                                
000046cf  Motor_Create                         
00004a05  Motor_GetSpeed                       
00005dd5  Motor_Init                           
202003d8  Motor_Left                           
20200420  Motor_Right                          
0000432d  Motor_SetDuty                        
000024c5  Motor_SetSpeed                       
00004cdd  MyPrintf_DMA                         
000075a9  NMI_Handler                          
00001a7d  No_MCU_Ganv_Sensor_Init              
00004c69  No_MCU_Ganv_Sensor_Init_Frist        
00005ccd  No_Mcu_Ganv_Sensor_Task_Without_tick 
00002ba9  OLED_Init                            
000058e1  OLED_Printf                          
00002265  OLED_ShowChar                        
00004d4d  OLED_ShowString                      
00002841  PID_IQ_Prosc                         
000075a9  PendSV_Handler                       
000075a9  RTC_IRQHandler                       
000075ad  Reset_Handler                        
000075a9  SPI0_IRQHandler                      
000075a9  SPI1_IRQHandler                      
000075a9  SVC_Handler                          
0000592d  SYSCFG_DL_ADC1_init                  
00006519  SYSCFG_DL_DMA_CH_RX_init             
00007191  SYSCFG_DL_DMA_CH_TX_init             
00007535  SYSCFG_DL_DMA_init                   
0000138d  SYSCFG_DL_GPIO_init                  
00005505  SYSCFG_DL_I2C_MPU6050_init           
00005029  SYSCFG_DL_I2C_OLED_init              
00004761  SYSCFG_DL_Motor_PWM_init             
00005395  SYSCFG_DL_SYSCTL_init                
000074cd  SYSCFG_DL_SYSTICK_init               
00004879  SYSCFG_DL_UART0_init                 
00006639  SYSCFG_DL_init                       
000043cd  SYSCFG_DL_initPower                  
0000555d  Serial_Init                          
20200000  Serial_RxData                        
000072cf  SysGetTick                           
0000758f  SysTick_Handler                      
0000687d  SysTick_Increasment                  
00007541  Sys_GetTick                          
000075a9  TIMA0_IRQHandler                     
000075a9  TIMA1_IRQHandler                     
000075a9  TIMG0_IRQHandler                     
000075a9  TIMG12_IRQHandler                    
000075a9  TIMG6_IRQHandler                     
000075a9  TIMG7_IRQHandler                     
000075a9  TIMG8_IRQHandler                     
00007477  TI_memcpy_small                      
00007527  TI_memset_small                      
00003f25  Task_Add                             
00005e15  Task_GraySensor                      
00002fc9  Task_Init                            
00005bbd  Task_Key                             
00006315  Task_LED                             
000031b5  Task_Motor_PID                       
0000363d  Task_OLED                            
00003fd9  Task_Serial                          
000025f1  Task_Tracker                         
000075a9  UART0_IRQHandler                     
000075a9  UART1_IRQHandler                     
000075a9  UART2_IRQHandler                     
000075a9  UART3_IRQHandler                     
000071a9  _IQ24div                             
000071c1  _IQ24mpy                             
00006549  _IQ24toF                             
20208000  __STACK_END                          
00000200  __STACK_SIZE                         
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00008ce4  __TI_CINIT_Base                      
00008cf4  __TI_CINIT_Limit                     
00008cf4  __TI_CINIT_Warm                      
00008cd0  __TI_Handler_Table_Base              
00008cdc  __TI_Handler_Table_Limit             
000061ed  __TI_auto_init_nobinit_nopinit       
00004b05  __TI_decompress_lzss                 
00007489  __TI_decompress_none                 
000055b5  __TI_ltoa                            
ffffffff  __TI_pprof_out_hndl                  
000000c1  __TI_printfi                         
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
00000000  __TI_static_base__                   
000072e5  __TI_zero_init_nomemset              
000018f3  __adddf3                             
000039b7  __addsf3                             
000089e0  __aeabi_ctype_table_                 
000089e0  __aeabi_ctype_table_C                
000059c5  __aeabi_d2iz                         
00005d11  __aeabi_d2uiz                        
000018f3  __aeabi_dadd                         
0000508d  __aeabi_dcmpeq                       
000050c9  __aeabi_dcmpge                       
000050dd  __aeabi_dcmpgt                       
000050b5  __aeabi_dcmple                       
000050a1  __aeabi_dcmplt                       
00002cb9  __aeabi_ddiv                         
00003475  __aeabi_dmul                         
000018e9  __aeabi_dsub                         
202004f8  __aeabi_errno                        
00007599  __aeabi_errno_addr                   
00005e95  __aeabi_f2d                          
0000634d  __aeabi_f2iz                         
000039b7  __aeabi_fadd                         
000050f1  __aeabi_fcmpeq                       
0000512d  __aeabi_fcmpge                       
00005141  __aeabi_fcmpgt                       
00005119  __aeabi_fcmple                       
00005105  __aeabi_fcmplt                       
00004981  __aeabi_fdiv                         
000047ed  __aeabi_fmul                         
000039ad  __aeabi_fsub                         
00006691  __aeabi_i2d                          
00006175  __aeabi_i2f                          
00005665  __aeabi_idiv                         
00001a7b  __aeabi_idiv0                        
00005665  __aeabi_idivmod                      
00004287  __aeabi_ldiv0                        
00006abd  __aeabi_llsl                         
000069d5  __aeabi_lmul                         
000075a1  __aeabi_memcpy                       
000075a1  __aeabi_memcpy4                      
000075a1  __aeabi_memcpy8                      
000074fd  __aeabi_memset                       
000074fd  __aeabi_memset4                      
000074fd  __aeabi_memset8                      
000069b1  __aeabi_ui2d                         
000068a5  __aeabi_ui2f                         
00005e55  __aeabi_uidiv                        
00005e55  __aeabi_uidivmod                     
00007415  __aeabi_uldivmod                     
00006abd  __ashldi3                            
ffffffff  __binit__                            
00004e91  __cmpdf2                             
00006265  __cmpsf2                             
00002cb9  __divdf3                             
00004981  __divsf3                             
00004e91  __eqdf2                              
00006265  __eqsf2                              
00005e95  __extendsfdf2                        
000059c5  __fixdfsi                            
0000634d  __fixsfsi                            
00005d11  __fixunsdfsi                         
00006691  __floatsidf                          
00006175  __floatsisf                          
000069b1  __floatunsidf                        
000068a5  __floatunsisf                        
00004b81  __gedf2                              
000061b1  __gesf2                              
00004b81  __gtdf2                              
000061b1  __gtsf2                              
00004e91  __ledf2                              
00006265  __lesf2                              
00004e91  __ltdf2                              
00006265  __ltsf2                              
UNDEFED   __mpu_init                           
00003475  __muldf3                             
000069d5  __muldi3                             
000062a1  __muldsi3                            
000047ed  __mulsf3                             
00004e91  __nedf2                              
00006265  __nesf2                              
20207e00  __stack                              
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
000018e9  __subdf3                             
000039ad  __subsf3                             
00004289  __udivmoddi4                         
000068cd  _c_int00_noargs                      
UNDEFED   _system_post_cinit                   
000075c1  _system_pre_init                     
000075c5  abort                                
00005a59  adc_getValue                         
000087b6  asc2_0806                            
000081c6  asc2_1608                            
00005ed5  atoi                                 
ffffffff  binit                                
00004dbb  convertAnalogToDigital               
202004fc  delayTick                            
00005aa1  dmp_enable_6x_lp_quat                
00000a91  dmp_enable_feature                   
00005215  dmp_enable_gyro_cal                  
00005ae9  dmp_enable_lp_quat                   
00006d0d  dmp_load_motion_driver_firmware      
00007429  dmp_register_android_orient_cb       
0000743d  dmp_register_tap_cb                  
000045a1  dmp_set_fifo_rate                    
00001c05  dmp_set_orientation                  
00005c01  dmp_set_shake_reject_thresh          
00006455  dmp_set_shake_reject_time            
00006487  dmp_set_shake_reject_timeout         
00004f5f  dmp_set_tap_axes                     
00005c45  dmp_set_tap_count                    
00000d09  dmp_set_tap_thresh                   
000065d9  dmp_set_tap_time                     
00006609  dmp_set_tap_time_multi               
20200508  enable_group1_irq                    
000053f1  frexp                                
000053f1  frexpl                               
00008c14  hw                                   
00000000  interruptVectors                     
000038d5  ldexp                                
000038d5  ldexpl                               
202003a0  left_motor                           
000066bd  main                                 
000069f9  memccpy                              
00006a7d  memcmp                               
00005275  mpu6050_i2c_sda_unlock               
00003db1  mpu_configure_fifo                   
00004bf5  mpu_get_accel_fsr                    
000052d5  mpu_get_gyro_fsr                     
00006421  mpu_get_sample_rate                  
00002719  mpu_init                             
00002965  mpu_load_firmware                    
00002ec9  mpu_lp_accel_mode                    
00004085  mpu_read_mem                         
00000f41  mpu_reset_fifo                       
00003559  mpu_set_accel_fsr                    
00001749  mpu_set_bypass                       
00003e6d  mpu_set_dmp_state                    
00003c29  mpu_set_gyro_fsr                     
0000446d  mpu_set_int_latched                  
00003b59  mpu_set_lpf                          
000032a5  mpu_set_sample_rate                  
00002395  mpu_set_sensors                      
00004131  mpu_write_mem                        
00001ffd  mspm0_i2c_read                       
00003ced  mspm0_i2c_write                      
000041dd  normalizeAnalogValues                
00002131  qsort                                
00008b53  reg                                  
202003b4  right_motor                          
000038d5  scalbn                               
000038d5  scalbnl                              
00008b0c  test                                 
20200500  uwTick                               
00005f15  vsnprintf                            
000066e9  vsprintf                             
000074dd  wcslen                               


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                                 
-------   ----                                 
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00000000  __TI_static_base__                   
00000000  interruptVectors                     
000000c1  __TI_printfi                         
00000200  __STACK_SIZE                         
00000a91  dmp_enable_feature                   
00000d09  dmp_set_tap_thresh                   
00000f41  mpu_reset_fifo                       
0000138d  SYSCFG_DL_GPIO_init                  
00001749  mpu_set_bypass                       
000018e9  __aeabi_dsub                         
000018e9  __subdf3                             
000018f3  __adddf3                             
000018f3  __aeabi_dadd                         
00001a7b  __aeabi_idiv0                        
00001a7d  No_MCU_Ganv_Sensor_Init              
00001c05  dmp_set_orientation                  
00001d7d  MPU6050_Init                         
00001ffd  mspm0_i2c_read                       
00002131  qsort                                
00002265  OLED_ShowChar                        
00002395  mpu_set_sensors                      
000024c5  Motor_SetSpeed                       
000025f1  Task_Tracker                         
00002719  mpu_init                             
00002841  PID_IQ_Prosc                         
00002965  mpu_load_firmware                    
00002ba9  OLED_Init                            
00002cb9  __aeabi_ddiv                         
00002cb9  __divdf3                             
00002dc5  DL_Timer_initFourCCPWMMode           
00002ec9  mpu_lp_accel_mode                    
00002fc9  Task_Init                            
000031b5  Task_Motor_PID                       
000032a5  mpu_set_sample_rate                  
00003391  GROUP1_IRQHandler                    
00003475  __aeabi_dmul                         
00003475  __muldf3                             
00003559  mpu_set_accel_fsr                    
0000363d  Task_OLED                            
0000371d  DL_SYSCTL_configSYSPLL               
000037f9  Get_Analog_value                     
000038d5  ldexp                                
000038d5  ldexpl                               
000038d5  scalbn                               
000038d5  scalbnl                              
000039ad  __aeabi_fsub                         
000039ad  __subsf3                             
000039b7  __addsf3                             
000039b7  __aeabi_fadd                         
00003b59  mpu_set_lpf                          
00003c29  mpu_set_gyro_fsr                     
00003ced  mspm0_i2c_write                      
00003db1  mpu_configure_fifo                   
00003e6d  mpu_set_dmp_state                    
00003f25  Task_Add                             
00003fd9  Task_Serial                          
00004085  mpu_read_mem                         
00004131  mpu_write_mem                        
000041dd  normalizeAnalogValues                
00004287  __aeabi_ldiv0                        
00004289  __udivmoddi4                         
0000432d  Motor_SetDuty                        
000043cd  SYSCFG_DL_initPower                  
0000446d  mpu_set_int_latched                  
00004509  I2C_OLED_WR_Byte                     
000045a1  dmp_set_fifo_rate                    
000046cf  Motor_Create                         
00004761  SYSCFG_DL_Motor_PWM_init             
000047ed  __aeabi_fmul                         
000047ed  __mulsf3                             
00004879  SYSCFG_DL_UART0_init                 
00004981  __aeabi_fdiv                         
00004981  __divsf3                             
00004a05  Motor_GetSpeed                       
00004b05  __TI_decompress_lzss                 
00004b81  __gedf2                              
00004b81  __gtdf2                              
00004bf5  mpu_get_accel_fsr                    
00004c69  No_MCU_Ganv_Sensor_Init_Frist        
00004cdd  MyPrintf_DMA                         
00004d4d  OLED_ShowString                      
00004dbb  convertAnalogToDigital               
00004e27  I2C_OLED_Clear                       
00004e91  __cmpdf2                             
00004e91  __eqdf2                              
00004e91  __ledf2                              
00004e91  __ltdf2                              
00004e91  __nedf2                              
00004f5f  dmp_set_tap_axes                     
00004fc5  DL_SYSCTL_setHFCLKSourceHFXTParams   
00005029  SYSCFG_DL_I2C_OLED_init              
0000508d  __aeabi_dcmpeq                       
000050a1  __aeabi_dcmplt                       
000050b5  __aeabi_dcmple                       
000050c9  __aeabi_dcmpge                       
000050dd  __aeabi_dcmpgt                       
000050f1  __aeabi_fcmpeq                       
00005105  __aeabi_fcmplt                       
00005119  __aeabi_fcmple                       
0000512d  __aeabi_fcmpge                       
00005141  __aeabi_fcmpgt                       
00005155  I2C_OLED_i2c_sda_unlock              
000051b5  Key_Read                             
00005215  dmp_enable_gyro_cal                  
00005275  mpu6050_i2c_sda_unlock               
000052d5  mpu_get_gyro_fsr                     
00005335  DL_I2C_fillControllerTXFIFO          
00005395  SYSCFG_DL_SYSCTL_init                
000053f1  frexp                                
000053f1  frexpl                               
00005505  SYSCFG_DL_I2C_MPU6050_init           
0000555d  Serial_Init                          
000055b5  __TI_ltoa                            
00005665  __aeabi_idiv                         
00005665  __aeabi_idivmod                      
00005849  DL_DMA_initChannel                   
000058e1  OLED_Printf                          
0000592d  SYSCFG_DL_ADC1_init                  
000059c5  __aeabi_d2iz                         
000059c5  __fixdfsi                            
00005a11  DL_UART_init                         
00005a59  adc_getValue                         
00005aa1  dmp_enable_6x_lp_quat                
00005ae9  dmp_enable_lp_quat                   
00005b79  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00005bbd  Task_Key                             
00005c01  dmp_set_shake_reject_thresh          
00005c45  dmp_set_tap_count                    
00005ccd  No_Mcu_Ganv_Sensor_Task_Without_tick 
00005d11  __aeabi_d2uiz                        
00005d11  __fixunsdfsi                         
00005d55  DL_ADC12_setClockConfig              
00005d95  Interrupt_Init                       
00005dd5  Motor_Init                           
00005e15  Task_GraySensor                      
00005e55  __aeabi_uidiv                        
00005e55  __aeabi_uidivmod                     
00005e95  __aeabi_f2d                          
00005e95  __extendsfdf2                        
00005ed5  atoi                                 
00005f15  vsnprintf                            
00006085  DL_I2C_flushControllerTXFIFO         
000060fd  Get_Anolog_Value                     
00006139  I2C_OLED_Set_Pos                     
00006175  __aeabi_i2f                          
00006175  __floatsisf                          
000061b1  __gesf2                              
000061b1  __gtsf2                              
000061ed  __TI_auto_init_nobinit_nopinit       
00006265  __cmpsf2                             
00006265  __eqsf2                              
00006265  __lesf2                              
00006265  __ltsf2                              
00006265  __nesf2                              
000062a1  __muldsi3                            
000062db  Get_Normalize_For_User               
00006315  Task_LED                             
0000634d  __aeabi_f2iz                         
0000634d  __fixsfsi                            
00006421  mpu_get_sample_rate                  
00006455  dmp_set_shake_reject_time            
00006487  dmp_set_shake_reject_timeout         
00006519  SYSCFG_DL_DMA_CH_RX_init             
00006549  _IQ24toF                             
000065d9  dmp_set_tap_time                     
00006609  dmp_set_tap_time_multi               
00006639  SYSCFG_DL_init                       
00006691  __aeabi_i2d                          
00006691  __floatsidf                          
000066bd  main                                 
000066e9  vsprintf                             
0000687d  SysTick_Increasment                  
000068a5  __aeabi_ui2f                         
000068a5  __floatunsisf                        
000068cd  _c_int00_noargs                      
00006967  DL_I2C_setClockConfig                
000069b1  __aeabi_ui2d                         
000069b1  __floatunsidf                        
000069d5  __aeabi_lmul                         
000069d5  __muldi3                             
000069f9  memccpy                              
00006a5d  Delay                                
00006a7d  memcmp                               
00006abd  __aeabi_llsl                         
00006abd  __ashldi3                            
00006cb9  DL_Timer_setCaptCompUpdateMethod     
00006cd5  DL_Timer_setClockConfig              
00006d0d  dmp_load_motion_driver_firmware      
000070e9  DL_Timer_setCaptureCompareOutCtl     
00007191  SYSCFG_DL_DMA_CH_TX_init             
000071a9  _IQ24div                             
000071c1  _IQ24mpy                             
000072cf  SysGetTick                           
000072e5  __TI_zero_init_nomemset              
00007415  __aeabi_uldivmod                     
00007429  dmp_register_android_orient_cb       
0000743d  dmp_register_tap_cb                  
00007465  DL_UART_setClockConfig               
00007477  TI_memcpy_small                      
00007489  __TI_decompress_none                 
000074bd  DL_Timer_setCaptureCompareValue      
000074cd  SYSCFG_DL_SYSTICK_init               
000074dd  wcslen                               
000074ed  Get_Digtal_For_User                  
000074fd  __aeabi_memset                       
000074fd  __aeabi_memset4                      
000074fd  __aeabi_memset8                      
00007527  TI_memset_small                      
00007535  SYSCFG_DL_DMA_init                   
00007541  Sys_GetTick                          
0000754d  DL_Common_delayCycles                
0000758f  SysTick_Handler                      
00007599  __aeabi_errno_addr                   
000075a1  __aeabi_memcpy                       
000075a1  __aeabi_memcpy4                      
000075a1  __aeabi_memcpy8                      
000075a9  ADC0_IRQHandler                      
000075a9  ADC1_IRQHandler                      
000075a9  AES_IRQHandler                       
000075a9  CANFD0_IRQHandler                    
000075a9  DAC0_IRQHandler                      
000075a9  DMA_IRQHandler                       
000075a9  Default_Handler                      
000075a9  GROUP0_IRQHandler                    
000075a9  HardFault_Handler                    
000075a9  I2C0_IRQHandler                      
000075a9  I2C1_IRQHandler                      
000075a9  NMI_Handler                          
000075a9  PendSV_Handler                       
000075a9  RTC_IRQHandler                       
000075a9  SPI0_IRQHandler                      
000075a9  SPI1_IRQHandler                      
000075a9  SVC_Handler                          
000075a9  TIMA0_IRQHandler                     
000075a9  TIMA1_IRQHandler                     
000075a9  TIMG0_IRQHandler                     
000075a9  TIMG12_IRQHandler                    
000075a9  TIMG6_IRQHandler                     
000075a9  TIMG7_IRQHandler                     
000075a9  TIMG8_IRQHandler                     
000075a9  UART0_IRQHandler                     
000075a9  UART1_IRQHandler                     
000075a9  UART2_IRQHandler                     
000075a9  UART3_IRQHandler                     
000075ad  Reset_Handler                        
000075c1  _system_pre_init                     
000075c4  C$$EXIT                              
000075c5  abort                                
000081c6  asc2_1608                            
000087b6  asc2_0806                            
000089e0  __aeabi_ctype_table_                 
000089e0  __aeabi_ctype_table_C                
00008b0c  test                                 
00008b53  reg                                  
00008c14  hw                                   
00008cd0  __TI_Handler_Table_Base              
00008cdc  __TI_Handler_Table_Limit             
00008ce4  __TI_CINIT_Base                      
00008cf4  __TI_CINIT_Limit                     
00008cf4  __TI_CINIT_Warm                      
20200000  Serial_RxData                        
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
202002f0  GraySensor                           
202003a0  left_motor                           
202003b4  right_motor                          
202003c8  Data_Pitch                           
202003cc  Data_Roll                            
202003d0  Data_Yaw                             
202003d4  ExISR_Flag                           
202003d8  Motor_Left                           
20200420  Motor_Right                          
20200494  Gray_Anolog                          
202004a4  Gray_Normal                          
202004db  Data_Tracker_Input                   
202004e3  Flag_LED                             
202004e4  Motor                                
202004ec  Data_MotorEncoder                    
202004f0  Data_Motor_TarSpeed                  
202004f4  Data_Tracker_Offset                  
202004f8  __aeabi_errno                        
202004fc  delayTick                            
20200500  uwTick                               
20200504  Flag_MPU6050_Ready                   
20200505  Gray_Digtal                          
20200508  enable_group1_irq                    
20207e00  __stack                              
20208000  __STACK_END                          
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
ffffffff  __binit__                            
ffffffff  binit                                
UNDEFED   __mpu_init                           
UNDEFED   _system_post_cinit                   

[306 symbols]
